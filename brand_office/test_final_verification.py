#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final verification test cho logic đã sửa
Test toàn bộ workflow với multiple batches
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from update_brand_office_address import BrandOfficeAddressUpdater
import logging
import time

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('exports/test_final_verification.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FinalVerificationTest(BrandOfficeAddressUpdater):
    def __init__(self):
        super().__init__(batch_size=25)  # Test với batch size vừa phải
        logger.info("🧪 Final Verification Test initialized")
    
    def get_total_brand_office_count(self):
        return 100  # Test với 100 records, 4 batches
    
    def test_full_workflow(self):
        """Test toàn bộ workflow với multiple batches"""
        try:
            logger.info("🧪 Testing full workflow with multiple batches...")
            
            # Test run method
            results = self.run()
            
            if results:
                logger.info("✅ Full workflow test completed successfully!")
                return True
            else:
                logger.error("❌ Full workflow test failed!")
                return False
                
        except Exception as e:
            logger.error(f"❌ Full workflow test failed: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    def process_all_data(self, geo_ward_data, output_file='exports/test_final_verification_results.csv'):
        """Override để test với limit"""
        logger.info("🧪 Starting FINAL VERIFICATION data processing...")
        
        # Lấy tổng số records để tracking (test mode)
        total_records = self.get_total_brand_office_count()
        estimated_batches = (total_records + self.batch_size - 1) // self.batch_size
        
        logger.info(f"📊 FINAL VERIFICATION Processing plan:")
        logger.info(f"   - Total records: {total_records:,}")
        logger.info(f"   - Batch size: {self.batch_size:,}")
        logger.info(f"   - Estimated batches: {estimated_batches}")
        logger.info(f"   - Output file: {output_file}")
        
        # Initialize CSV file with headers
        import pandas as pd
        headers = ['id', 'latitude', 'longitude', 'city_id',
                  'geo_province_code', 'ward_code', 'province_title', 'ward_title', 
                  'address_old', 'new_address', 'status']
        pd.DataFrame(columns=headers).to_csv(output_file, index=False)
        
        total_processed = 0
        total_success = 0
        total_errors = 0
        offset = 0
        batch_number = 0
        max_records = 100  # Limit for test
        
        logger.info("=" * 80)
        logger.info("🔄 STARTING FINAL VERIFICATION BATCH PROCESSING")
        logger.info("=" * 80)
        
        # Process data in batches
        while total_processed < max_records:
            # Lấy batch data
            remaining = max_records - total_processed
            current_batch_size = min(self.batch_size, remaining)
            
            batch_data = self.get_brand_office_data(offset, current_batch_size)
            if not batch_data:
                break
                
            batch_number += 1
            
            # Process batch with enhanced tracking
            batch_results = self.process_batch(batch_data, geo_ward_data, batch_number, estimated_batches)
            
            # Save to CSV immediately (streaming write)
            if batch_results:
                batch_df = pd.DataFrame(batch_results)
                batch_df.to_csv(output_file, mode='a', header=False, index=False)
                logger.info(f"💾 Saved {len(batch_results)} results to CSV")
            
            # Update stats
            batch_success = len([r for r in batch_results if r.get('status') == 'matched'])
            batch_errors = len(batch_results) - batch_success
            
            total_processed += len(batch_results)
            total_success += batch_success
            total_errors += batch_errors
            
            # Enhanced progress tracking
            progress_pct = (total_processed / max_records * 100) if max_records > 0 else 0
            elapsed_time = time.time() - self.start_time
            estimated_total_time = (elapsed_time / progress_pct * 100) if progress_pct > 0 else 0
            remaining_time = estimated_total_time - elapsed_time if estimated_total_time > elapsed_time else 0
            
            logger.info(f"📊 Progress: {total_processed:,}/{max_records:,} ({progress_pct:.1f}%) | "
                       f"Success: {total_success:,} | Errors: {total_errors:,}")
            logger.info(f"⏱️  Time: {elapsed_time/60:.1f}m elapsed | ~{remaining_time/60:.1f}m remaining")
            
            # Update processed count for overall tracking
            self.processed_count = total_processed
            
            offset += self.batch_size
            
            # Small delay
            time.sleep(0.1)
        
        # Enhanced final stats
        success_rate = total_success / total_processed * 100 if total_processed > 0 else 0
        total_elapsed = time.time() - self.start_time
        overall_rpm = total_processed / (total_elapsed / 60) if total_elapsed > 0 else 0
        
        logger.info("=" * 80)
        logger.info("🎉 FINAL VERIFICATION PROCESSING COMPLETED!")
        logger.info("=" * 80)
        logger.info(f"📊 Final Statistics:")
        logger.info(f"   - Total processed: {total_processed:,} records")
        logger.info(f"   - Success: {total_success:,} ({success_rate:.1f}%)")
        logger.info(f"   - Errors: {total_errors:,}")
        logger.info(f"   - Total time: {total_elapsed/60:.1f} minutes")
        logger.info(f"   - Overall RPM: {overall_rpm:.0f}")
        logger.info(f"   - Average batch time: {(total_elapsed/batch_number):.2f}s" if batch_number > 0 else "")
        logger.info(f"   - Output file: {output_file}")
        logger.info("=" * 80)
        
        return {
            'total_processed': total_processed,
            'total_success': total_success,
            'total_errors': total_errors,
            'success_rate': success_rate,
            'output_file': output_file
        }

def main():
    """Chạy final verification test"""
    logger.info("🧪 STARTING FINAL VERIFICATION TEST")
    logger.info("=" * 80)
    
    try:
        tester = FinalVerificationTest()
        success = tester.test_full_workflow()
        
        if success:
            logger.info("🎉 FINAL VERIFICATION TEST PASSED!")
            logger.info("✅ Logic đã hoạt động đúng và ổn định!")
            logger.info("🚀 Sẵn sàng cho production với 30k records!")
        else:
            logger.error("❌ FINAL VERIFICATION TEST FAILED!")
            
    except Exception as e:
        logger.error(f"❌ Final verification error: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
