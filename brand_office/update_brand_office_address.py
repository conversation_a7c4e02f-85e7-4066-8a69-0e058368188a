#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cập nhật địa chỉ brand_office dựa trên geometry data (Simplified Version)
Task: Sử dụng dữ liệu geometry để xác định ch<PERSON>h xác xã/phường và tỉnh/thành phố
"""

import asyncio
import json
import logging
import pandas as pd
import geopandas as gpd
import mysql.connector
from mysql.connector import Error
from shapely.geometry import Point, shape
import warnings
from typing import List, Dict
from gemini import Gemini
warnings.filterwarnings('ignore')
import time


# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('exports/update_address.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BrandOfficeAddressUpdater:
    def __init__(self, batch_size=400):
        self.connection = None
        self.results = []
        self.batch_size = batch_size  # Optimized for 30k records
        self.processed_count = 0
        self.matched_count = 0
        self.unmatched_count = 0
        self.gemini = Gemini()
        self.start_time = time.time()
        logger.info(f"🚀 Optimized BrandOfficeAddressUpdater initialized (batch_size: {batch_size})")

    def get_database_connection(self):
        """Tạo kết nối database"""
        try:
            connection = mysql.connector.connect(
                host='127.0.0.1',
                port=3306,
                database='urbox',
                user='root',
                password='root',
                charset='utf8mb4'
            )
            
            if connection.is_connected():
                logger.info("✅ Kết nối database thành công!")
                return connection
                
        except Error as e:
            logger.error(f"❌ Lỗi kết nối database: {e}")
            return None
    
    def get_brand_office_data(self, offset=0, limit=2000):
        """Lấy dữ liệu brand_office theo batch"""
        query = """
        SELECT id, latitude, longitude, city_id, address_old
        FROM brand_office
        WHERE address_old IS NOT NULL
        AND address_old != ''
        AND latitude IS NOT NULL
        AND longitude IS NOT NULL
        and status = 2
        LIMIT %s OFFSET %s
        """

        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query, (limit, offset))
            results = cursor.fetchall()
            cursor.close()

            logger.info(f"📊 Lấy được {len(results)} records brand_office (offset: {offset})")
            return results

        except Error as e:
            logger.error(f"❌ Lỗi lấy dữ liệu brand_office: {e}")
            return []

    def get_total_brand_office_count(self):
        """Lấy tổng số records để tracking progress"""
        query = """
        SELECT COUNT(*) as total
        FROM brand_office
        WHERE address_old IS NOT NULL
        AND address_old != ''
        AND latitude IS NOT NULL
        AND longitude IS NOT NULL
        and status = 2
        """

        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query)
            result = cursor.fetchone()
            cursor.close()

            total = result['total'] if result else 0
            logger.info(f"📊 Tổng số brand_office records: {total}")
            return total

        except Error as e:
            logger.error(f"❌ Lỗi đếm brand_office: {e}")
            return 0

    def get_geo_ward_data(self):
        """Lấy dữ liệu geo_ward và trả về GeoDataFrame với spatial index"""

        query = """
        SELECT geometry, geo_province_code, province_title, ward_title, code
        FROM geo_ward
        WHERE geometry IS NOT NULL
        """

        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query)
            results = cursor.fetchall()
            cursor.close()

            logger.info(f"📊 Lấy được {len(results)} geo_ward records")

            # Chuyển đổi sang GeoDataFrame
            gdf_data = []
            for row in results:
                try:
                    # Parse geometry từ JSON string
                    if isinstance(row['geometry'], str):
                        geometry_data = json.loads(row['geometry'])
                    else:
                        geometry_data = row['geometry']

                    geometry = shape(geometry_data)

                    gdf_data.append({
                        'geometry': geometry,
                        'geo_province_code': row['geo_province_code'],
                        'province_title': row['province_title'],
                        'ward_title': row['ward_title'],
                        'code': row['code']
                    })
                except Exception as e:
                    logger.warning(f"⚠️ Lỗi parse geometry cho ward {row.get('code', 'unknown')}: {e}")
                    continue

            if not gdf_data:
                logger.error("❌ Không có geometry hợp lệ nào")
                return gpd.GeoDataFrame()

            # Tạo GeoDataFrame
            gdf = gpd.GeoDataFrame(gdf_data, crs='EPSG:4326')

            # Tạo spatial index để tối ưu hóa tìm kiếm
            logger.info("🔍 Tạo spatial index cho GeoDataFrame...")
            gdf.sindex  # Trigger spatial index creation
            logger.info(f"✅ Đã tạo GeoDataFrame với {len(gdf)} records và spatial index")
            return gdf

        except Error as e:
            logger.error(f"❌ Lỗi lấy geo_ward data: {e}")
            return gpd.GeoDataFrame()
    
    def parse_geometry(self, geometry_str):
        """Parse geometry từ JSON string thành Shapely object"""
        try:
            if isinstance(geometry_str, str):
                geometry_data = json.loads(geometry_str)
            else:
                geometry_data = geometry_str
                
            return shape(geometry_data)
        except Exception as e:
            logger.warning(f"⚠️ Lỗi parse geometry: {e}")
            return None
    
    def find_ward_by_lat_lng(self, lat, lng, geo_ward_gdf):
        """Tìm ward chứa tọa độ sử dụng spatial index (tối ưu hóa)"""

        try:
            point = Point(lng, lat)

            if len(geo_ward_gdf) == 0:
                return None, 'no_data'

            logger.debug(f"🔍 Tìm kiếm spatial index trong {len(geo_ward_gdf)} wards cho tọa độ ({lat}, {lng})")

            # Sử dụng spatial index để tìm candidates nhanh chóng
            try:
                # Lấy possible matches từ spatial index
                possible_matches_idx = list(geo_ward_gdf.sindex.intersection(point.bounds))

                if not possible_matches_idx:
                    logger.debug(f"🔍 Không tìm thấy candidates từ spatial index")
                    
                    return None, 'no_match'

                logger.debug(f"🔍 Spatial index tìm được {len(possible_matches_idx)} candidates")

                # Kiểm tra chính xác các candidates
                for idx in possible_matches_idx:
                    try:
                        ward_row = geo_ward_gdf.iloc[idx]
                        geometry = ward_row.geometry

                        if geometry:
                            # Thử contains() trước (chính xác hơn)
                            if geometry.contains(point):
                                ward_dict = ward_row.to_dict()
                                
                                return ward_dict, 'contains'
                            # Fallback: intersects() (cho trường hợp point ở biên)
                            elif geometry.intersects(point):
                                ward_dict = ward_row.to_dict()
                                
                                return ward_dict, 'intersects'
                    except Exception as candidate_error:
                        logger.warning(f"⚠️ Lỗi xử lý candidate {idx}: {candidate_error}")
                        continue

                # Fallback: buffer search cho các candidates
                for idx in possible_matches_idx:
                    try:
                        ward_row = geo_ward_gdf.iloc[idx]
                        geometry = ward_row.geometry

                        if geometry and geometry.buffer(0.001).intersects(point):
                            ward_dict = ward_row.to_dict()
                            
                            return ward_dict, 'buffer'
                    except Exception as buffer_error:
                        logger.warning(f"⚠️ Lỗi buffer search candidate {idx}: {buffer_error}")
                        continue

                
                return None, 'no_match'

            except Exception as spatial_error:
                logger.warning(f"⚠️ Lỗi spatial index, fallback to linear search: {spatial_error}")
                # Fallback to linear search nếu spatial index lỗi
               
                try:
                    # Convert GeoDataFrame to records safely
                    geo_ward_records = geo_ward_gdf.to_dict('records')
                    return self.find_ward_by_lat_lng_legacy(lat, lng, geo_ward_records)                    
                    
                except Exception as fallback_error:
                    logger.error(f"❌ Lỗi fallback search: {fallback_error}")
                    
                    return None, 'error'

        except Exception as e:
            logger.warning(f"⚠️ Lỗi tìm ward tại ({lat}, {lng}): {e}")
            return None, 'error'

    def find_ward_by_lat_lng_legacy(self, lat, lng, geo_ward_data):
        """Hàm tìm kiếm legacy (linear search) để backward compatibility"""
        try:
            point = Point(lng, lat)

            logger.debug(f"🔍 Linear search trong {len(geo_ward_data)} wards cho tọa độ ({lat}, {lng})")

            for ward in geo_ward_data:
                try:
                    geometry = None

                    if isinstance(ward, dict):
                        geometry = self.parse_geometry(ward['geometry'])
                    elif hasattr(ward, 'geometry'):
                        # Pandas Series hoặc object có geometry attribute
                        geometry = ward.geometry
                    elif hasattr(ward, 'get') and callable(ward.get):
                        # Object có method get()
                        geometry = self.parse_geometry(ward.get('geometry'))
                    else:
                        # Skip nếu không thể xử lý
                        logger.debug(f"⚠️ Không thể xử lý ward type: {type(ward)}")
                        continue

                    if geometry:
                        # Thử contains() trước (chính xác hơn)
                        if geometry.contains(point):
                            return ward, 'contains'
                        # Fallback: intersects() (cho trường hợp point ở biên)
                        elif geometry.intersects(point):
                            return ward, 'intersects'
                        # Fallback: buffer cho trường hợp point ở gần đó
                        elif geometry.buffer(0.001).intersects(point):
                            return ward, 'buffer'

                except Exception as ward_error:
                    logger.debug(f"⚠️ Lỗi xử lý ward: {ward_error}")
                    continue
            return None, 'no_match'

        except Exception as e:
            logger.warning(f"⚠️ Lỗi legacy search tại ({lat}, {lng}): {e}")
            return None, 'error'

    def save_results_to_csv(self, results, filename='brand_office_updated.csv'):
        """Lưu kết quả ra CSV"""
        try:
            df = pd.DataFrame(results)
            filepath = f"exports/{filename}"
            df.to_csv(filepath, index=False, encoding='utf-8')
            
            logger.info(f"✅ Đã lưu {len(results)} records vào {filepath}")
            
            # Thống kê
            matched = len([r for r in results if r['status'] == 'matched'])
            unmatched = len([r for r in results if r['status'] == 'unmatched'])
            
            logger.info(f"📊 THỐNG KÊ: Matched: {matched}, Unmatched: {unmatched}")
            
        except Exception as e:
            logger.error(f"❌ Lỗi lưu CSV: {e}")

    async def process_batch(self, batch_data: List[Dict], geo_ward_data, batch_number=0, total_batches=0):
        """Xử lý một batch dữ liệu đồng bộ với enhanced progress tracking"""
        batch_start_time = time.time()

        # Enhanced progress logging
        if total_batches > 0:
            logger.info(f"🔄 Processing batch {batch_number}/{total_batches} ({len(batch_data)} items)")
        else:
            logger.info(f"🔄 Processing batch of {len(batch_data)} items")

        results = []
        chat = await self.gemini.start_chat()
        async for record in batch_data:
            try:
                # Tìm ward từ tọa độ
                ward_info, _ = await self.gemini.convert_address_async(
                    chat,
                    float(record['latitude']),
                    float(record['longitude']),
                    geo_ward_data
                )

                if ward_info and isinstance(ward_info, dict):
                    # Validate required fields
                    required_fields = ['ward_title', 'province_title', 'code', 'geo_province_code']
                    if all(field in ward_info for field in required_fields):
                        # Gọi Gemini để convert address (simplified)
                        new_address = self._convert_address_simple(
                            record['address_old'],
                            ward_info['ward_title'],
                            ward_info['province_title']
                        )

                        result = {
                            'id': record.get('id', '-1'),
                            'latitude': record.get('latitude', ''),
                            'longitude': record.get('longitude', ''),
                            'city_id': record.get('city_id', ''),
                            'geo_province_code': ward_info['geo_province_code'],
                            'ward_code': ward_info['code'],
                            'province_title': ward_info['province_title'],
                            'ward_title': ward_info['ward_title'],
                            'address_old': record.get('address_old', ''),
                            'new_address': new_address,
                            'status': 'matched'
                        }
                        self.matched_count += 1
                    else:
                        logger.warning(f"⚠️ Ward info missing required fields for record {record.get('id')}")
                        result = self._create_error_result(record, 'missing_fields')
                        self.unmatched_count += 1
                else:
                    logger.debug(f"🔍 No ward found for record {record.get('id')} at ({record.get('latitude')}, {record.get('longitude')})")
                    result = self._create_error_result(record, 'no_ward_found')
                    self.unmatched_count += 1

                results.append(result)

            except Exception as e:
                logger.error(f"❌ Error processing record {record.get('id', 'unknown')}: {e}")
                result = self._create_error_result(record, 'processing_error')
                results.append(result)
                self.unmatched_count += 1
        chat = None
        batch_time = time.time() - batch_start_time
        batch_rpm = len(batch_data) / (batch_time / 60) if batch_time > 0 else 0
        success_count = len([r for r in results if r.get('status') == 'matched'])

        # Enhanced batch completion logging
        elapsed_total = time.time() - self.start_time
        overall_rpm = (self.processed_count + len(batch_data)) / (elapsed_total / 60) if elapsed_total > 0 else 0

        logger.info(f"✅ Batch completed: {len(batch_data)} items in {batch_time:.2f}s ({batch_rpm:.0f} RPM) | Success: {success_count}/{len(batch_data)}")
        logger.info(f"📊 Overall progress: {self.processed_count + len(batch_data)} processed | {overall_rpm:.0f} overall RPM | {elapsed_total/60:.1f} min elapsed")

        return results

    def _create_error_result(self, record: Dict, error_type: str) -> Dict:
        """Tạo result cho trường hợp lỗi"""
        return {
            'id': record.get('id', 'unknown'),
            'latitude': record.get('latitude', ''),
            'longitude': record.get('longitude', ''),
            'city_id': record.get('city_id', ''),
            'geo_province_code': '',
            'ward_code': '',
            'province_title': '',
            'ward_title': '',
            'address_old': record.get('address_old', ''),
            'new_address': f'ERROR_{error_type.upper()}',
            'status': 'error'
        }

    def _convert_address_simple(self, old_address: str, new_ward: str, new_province: str) -> str:
        """
        Simplified address conversion without AI
        Chỉ thay thế thông tin ward và province cơ bản
        """
        try:
            # Đơn giản hóa: chỉ ghép địa chỉ cũ với ward và province mới
            # Loại bỏ các từ khóa về quận/huyện/tỉnh cũ
            import re

            # Danh sách từ khóa cần loại bỏ
            remove_patterns = [
                r',?\s*(quận|huyện|thị xã|thành phố)\s+[^,]+',
                r',?\s*(tỉnh|thành phố)\s+[^,]*$',
                r',?\s*(xã|phường|thị trấn)\s+[^,]+',
            ]

            cleaned_address = old_address
            for pattern in remove_patterns:
                cleaned_address = re.sub(pattern, '', cleaned_address, flags=re.IGNORECASE)

            # Làm sạch dấu phẩy thừa
            cleaned_address = re.sub(r',\s*,', ',', cleaned_address)
            cleaned_address = cleaned_address.strip().strip(',').strip()

            # Ghép với thông tin mới
            new_address = f"{cleaned_address}, {new_ward}, {new_province}"

            return new_address

        except Exception as e:
            logger.warning(f"⚠️ Lỗi convert address: {e}")
            return f"{old_address}, {new_ward}, {new_province}"

    async def process_all_data(self, geo_ward_data, output_file='exports/brand_offices_updated.csv'):
        """Xử lý toàn bộ dữ liệu với enhanced progress tracking"""
        logger.info("🚀 Starting optimized data processing...")

        # Lấy tổng số records để tracking
        total_records = self.get_total_brand_office_count()
        estimated_batches = (total_records + self.batch_size - 1) // self.batch_size  # Ceiling division

        logger.info(f"📊 Processing plan:")
        logger.info(f"   - Total records: {total_records:,}")
        logger.info(f"   - Batch size: {self.batch_size:,}")
        logger.info(f"   - Estimated batches: {estimated_batches}")
        logger.info(f"   - Output file: {output_file}")

        # Initialize CSV file with headers
        headers = ['id', 'latitude', 'longitude', 'city_id',
                  'geo_province_code', 'ward_code', 'province_title', 'ward_title',
                  'address_old', 'new_address', 'status']
        pd.DataFrame(columns=headers).to_csv(output_file, index=False)

        total_processed = 0
        total_success = 0
        total_errors = 0
        offset = 0
        batch_number = 0

        logger.info("=" * 80)
        logger.info("🔄 STARTING BATCH PROCESSING")
        logger.info("=" * 80)

        # Process data in batches
        while True:
            # Lấy batch data
            batch_data = self.get_brand_office_data(offset, self.batch_size)
            if not batch_data:
                break

            batch_number += 1

            # Process batch with enhanced tracking
            batch_results = await self.process_batch(batch_data, geo_ward_data, batch_number, estimated_batches)

            # Save to CSV immediately (streaming write)
            if batch_results:
                batch_df = pd.DataFrame(batch_results)
                batch_df.to_csv(output_file, mode='a', header=False, index=False)

            # Update stats
            batch_success = len([r for r in batch_results if r.get('status') == 'matched'])
            batch_errors = len(batch_results) - batch_success

            total_processed += len(batch_results)
            total_success += batch_success
            total_errors += batch_errors

            # Enhanced progress tracking
            progress_pct = (total_processed / total_records * 100) if total_records > 0 else 0
            elapsed_time = time.time() - self.start_time
            estimated_total_time = (elapsed_time / progress_pct * 100) if progress_pct > 0 else 0
            remaining_time = estimated_total_time - elapsed_time if estimated_total_time > elapsed_time else 0

            logger.info(f"📊 Progress: {total_processed:,}/{total_records:,} ({progress_pct:.1f}%) | "
                       f"Success: {total_success:,} | Errors: {total_errors:,}")
            logger.info(f"⏱️  Time: {elapsed_time/60:.1f}m elapsed | ~{remaining_time/60:.1f}m remaining")

            # Update processed count for overall tracking
            self.processed_count = total_processed

            offset += self.batch_size

            # Adaptive delay based on batch size
            logger.info(f"🔄 Sleeping for 60 seconds")
            time.sleep(60)

        # Enhanced final stats
        success_rate = total_success / total_processed * 100 if total_processed > 0 else 0
        total_elapsed = time.time() - self.start_time
        overall_rpm = total_processed / (total_elapsed / 60) if total_elapsed > 0 else 0

        logger.info("=" * 80)
        logger.info("🎉 DATA PROCESSING COMPLETED!")
        logger.info("=" * 80)
        logger.info(f"📊 Final Statistics:")
        logger.info(f"   - Total processed: {total_processed:,} records")
        logger.info(f"   - Success: {total_success:,} ({success_rate:.1f}%)")
        logger.info(f"   - Errors: {total_errors:,}")
        logger.info(f"   - Total time: {total_elapsed/60:.1f} minutes")
        logger.info(f"   - Overall RPM: {overall_rpm:.0f}")
        logger.info(f"   - Average batch time: {(total_elapsed/batch_number):.2f}s" if batch_number > 0 else "")
        logger.info(f"   - Output file: {output_file}")
        logger.info("=" * 80)

        return {
            'total_processed': total_processed,
            'total_success': total_success,
            'total_errors': total_errors,
            'success_rate': success_rate,
            'output_file': output_file
        }
    


    async def run(self):
        """Chạy toàn bộ process với logic đơn giản"""
        try:
            logger.info("🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (Simplified Version)")

            # Kết nối database
            self.connection = self.get_database_connection()
            if not self.connection:
                return

            # Lấy dữ liệu mapping và geo_ward
            logger.info("📊 Lấy dữ liệu mapping và geometry...")
            geo_ward_data = self.get_geo_ward_data()

            if geo_ward_data.empty:
                logger.error("❌ Không có dữ liệu geo_ward")
                return

            logger.info("=" * 60)
            logger.info("🔄 XỬ LÝ RECORDS")
            logger.info("=" * 60)

            # Process all data with simplified logic
            results = await self.process_all_data(
                geo_ward_data,
                output_file='exports/brand_offices_updated.csv'
            )

            logger.info(f"✅ UPDATED BRAND OFFICE ADDRESS COMPLETED! Processed: {results.get('total_processed', 0)}")

            # Log final statistics
            logger.info(f"📊 Final processing statistics:")
            logger.info(f"   - Matched: {self.matched_count}")
            logger.info(f"   - Unmatched: {self.unmatched_count}")

        except Exception as e:
            logger.error(f"❌ Lỗi trong quá trình xử lý: {e}")
        finally:
            if self.connection:
                self.connection.close()

if __name__ == "__main__":
    updater = BrandOfficeAddressUpdater()
    asyncio.run(updater.run())
