#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Production script cho việ<PERSON> cập nhật 30k brand_office records
Sử dụng phiên bản tối ưu với batch size và progress tracking
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from update_brand_office_address import BrandOfficeAddressUpdater
import logging
import time
from datetime import datetime

# Thiết lập logging cho production
log_filename = f"exports/production_update_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProductionBrandOfficeUpdater(BrandOfficeAddressUpdater):
    def __init__(self, batch_size=2000):
        """
        Production updater với batch size tối ưu cho 30k records
        """
        super().__init__(batch_size=batch_size)
        logger.info(f"🏭 Production mode initialized (batch_size: {batch_size})")
        
        # Production settings
        self.enable_checkpoints = True
        self.checkpoint_interval = 5  # Save checkpoint every 5 batches
        self.last_checkpoint_batch = 0
    
    def save_checkpoint(self, batch_number, total_processed, output_file):
        """Lưu checkpoint để có thể resume nếu bị gián đoạn"""
        if not self.enable_checkpoints:
            return
            
        checkpoint_data = {
            'batch_number': batch_number,
            'total_processed': total_processed,
            'timestamp': datetime.now().isoformat(),
            'output_file': output_file,
            'batch_size': self.batch_size
        }
        
        checkpoint_file = f"exports/checkpoint_{datetime.now().strftime('%Y%m%d')}.json"
        try:
            import json
            with open(checkpoint_file, 'w') as f:
                json.dump(checkpoint_data, f, indent=2)
            logger.info(f"💾 Checkpoint saved: batch {batch_number}, processed {total_processed}")
        except Exception as e:
            logger.warning(f"⚠️ Failed to save checkpoint: {e}")
    
    def process_all_data(self, geo_ward_data, output_file='exports/production_brand_offices_updated.csv'):
        """Production version với enhanced monitoring"""
        start_time = time.time()
        logger.info("🏭 Starting PRODUCTION data processing...")
        
        # Lấy tổng số records để tracking
        total_records = self.get_total_brand_office_count()
        estimated_batches = (total_records + self.batch_size - 1) // self.batch_size
        
        logger.info("=" * 100)
        logger.info("🏭 PRODUCTION PROCESSING PLAN")
        logger.info("=" * 100)
        logger.info(f"📊 Configuration:")
        logger.info(f"   - Total records: {total_records:,}")
        logger.info(f"   - Batch size: {self.batch_size:,}")
        logger.info(f"   - Estimated batches: {estimated_batches}")
        logger.info(f"   - Estimated time: ~{(total_records/20000):.1f} hours (assuming 20k RPM)")
        logger.info(f"   - Output file: {output_file}")
        logger.info(f"   - Log file: {log_filename}")
        logger.info(f"   - Checkpoints: {'Enabled' if self.enable_checkpoints else 'Disabled'}")
        
        # Initialize CSV file with headers
        import pandas as pd
        headers = ['id', 'latitude', 'longitude', 'city_id',
                  'geo_province_code', 'ward_code', 'province_title', 'ward_title', 
                  'address_old', 'new_address', 'status']
        pd.DataFrame(columns=headers).to_csv(output_file, index=False)
        logger.info(f"📄 CSV file initialized: {output_file}")
        
        total_processed = 0
        total_success = 0
        total_errors = 0
        offset = 0
        batch_number = 0
        
        logger.info("=" * 100)
        logger.info("🔄 STARTING PRODUCTION BATCH PROCESSING")
        logger.info("=" * 100)
        
        # Process data in batches
        while True:
            batch_start_time = time.time()
            
            # Lấy batch data
            batch_data = self.get_brand_office_data(offset, self.batch_size)
            if not batch_data:
                logger.info("✅ No more data to process")
                break
                
            batch_number += 1
            
            # Process batch with enhanced tracking
            batch_results = self.process_batch(batch_data, geo_ward_data, batch_number, estimated_batches)
            
            # Save to CSV immediately (streaming write)
            if batch_results:
                batch_df = pd.DataFrame(batch_results)
                batch_df.to_csv(output_file, mode='a', header=False, index=False)
            
            # Update stats
            batch_success = len([r for r in batch_results if r.get('status') == 'matched'])
            batch_errors = len(batch_results) - batch_success
            
            total_processed += len(batch_results)
            total_success += batch_success
            total_errors += batch_errors
            
            # Enhanced progress tracking
            progress_pct = (total_processed / total_records * 100) if total_records > 0 else 0
            elapsed_time = time.time() - start_time
            estimated_total_time = (elapsed_time / progress_pct * 100) if progress_pct > 0 else 0
            remaining_time = estimated_total_time - elapsed_time if estimated_total_time > elapsed_time else 0
            
            # Detailed progress logging
            logger.info("=" * 80)
            logger.info(f"📊 BATCH {batch_number}/{estimated_batches} COMPLETED")
            logger.info(f"   - Batch processed: {len(batch_results):,} records")
            logger.info(f"   - Batch success: {batch_success:,} ({batch_success/len(batch_results)*100:.1f}%)")
            logger.info(f"   - Overall progress: {total_processed:,}/{total_records:,} ({progress_pct:.1f}%)")
            logger.info(f"   - Success rate: {total_success:,}/{total_processed:,} ({total_success/total_processed*100:.1f}%)")
            logger.info(f"   - Time elapsed: {elapsed_time/60:.1f} minutes")
            logger.info(f"   - Estimated remaining: {remaining_time/60:.1f} minutes")
            logger.info(f"   - ETA: {datetime.fromtimestamp(time.time() + remaining_time).strftime('%H:%M:%S')}")
            
            # Update processed count for overall tracking
            self.processed_count = total_processed
            
            # Save checkpoint periodically
            if batch_number % self.checkpoint_interval == 0:
                self.save_checkpoint(batch_number, total_processed, output_file)
            
            offset += self.batch_size
            
            # Adaptive delay for production stability
            time.sleep(0.02)  # Small delay to prevent overwhelming the system
        
        # Final production stats
        total_elapsed = time.time() - start_time
        success_rate = total_success / total_processed * 100 if total_processed > 0 else 0
        overall_rpm = total_processed / (total_elapsed / 60) if total_elapsed > 0 else 0
        
        logger.info("=" * 100)
        logger.info("🎉 PRODUCTION PROCESSING COMPLETED!")
        logger.info("=" * 100)
        logger.info(f"📊 Final Production Statistics:")
        logger.info(f"   - Total processed: {total_processed:,} records")
        logger.info(f"   - Success: {total_success:,} ({success_rate:.1f}%)")
        logger.info(f"   - Errors: {total_errors:,}")
        logger.info(f"   - Total time: {total_elapsed/3600:.2f} hours ({total_elapsed/60:.1f} minutes)")
        logger.info(f"   - Overall RPM: {overall_rpm:.0f}")
        logger.info(f"   - Average batch time: {(total_elapsed/batch_number):.2f}s" if batch_number > 0 else "")
        logger.info(f"   - Batches processed: {batch_number}")
        logger.info(f"   - Output file: {output_file}")
        logger.info(f"   - Log file: {log_filename}")
        logger.info("=" * 100)
        
        # Final checkpoint
        self.save_checkpoint(batch_number, total_processed, output_file)
        
        return {
            'total_processed': total_processed,
            'total_success': total_success,
            'total_errors': total_errors,
            'success_rate': success_rate,
            'total_time_hours': total_elapsed/3600,
            'overall_rpm': overall_rpm,
            'output_file': output_file,
            'log_file': log_filename
        }

def main():
    """Chạy production update cho 30k records"""
    logger.info("🏭 STARTING PRODUCTION BRAND OFFICE UPDATE")
    logger.info(f"🕐 Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 100)
    
    try:
        # Tạo production updater với batch size tối ưu
        updater = ProductionBrandOfficeUpdater(batch_size=2000)
        
        # Chạy production update
        results = updater.run()
        
        logger.info("🎉 PRODUCTION UPDATE COMPLETED SUCCESSFULLY!")
        logger.info(f"🕐 End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return results
        
    except KeyboardInterrupt:
        logger.warning("⚠️ PRODUCTION UPDATE INTERRUPTED BY USER")
        logger.info("💾 Check checkpoint files for resume capability")
    except Exception as e:
        logger.error(f"❌ PRODUCTION UPDATE FAILED: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise

if __name__ == "__main__":
    main()
